:root {
  /* Primitive Color Tokens */
  --color-white: rgba(255, 255, 255, 1);
  --color-black: rgba(0, 0, 0, 1);
  --color-cream-50: rgba(252, 252, 249, 1);
  --color-cream-100: rgba(255, 255, 253, 1);
  --color-gray-200: rgba(245, 245, 245, 1);
  --color-gray-300: rgba(167, 169, 169, 1);
  --color-gray-400: rgba(119, 124, 124, 1);
  --color-slate-500: rgba(98, 108, 113, 1);
  --color-brown-600: rgba(94, 82, 64, 1);
  --color-charcoal-700: rgba(31, 33, 33, 1);
  --color-charcoal-800: rgba(38, 40, 40, 1);
  --color-slate-900: rgba(19, 52, 59, 1);
  --color-teal-300: rgba(50, 184, 198, 1);
  --color-teal-400: rgba(45, 166, 178, 1);
  --color-teal-500: rgba(33, 128, 141, 1);
  --color-teal-600: rgba(29, 116, 128, 1);
  --color-teal-700: rgba(26, 104, 115, 1);
  --color-teal-800: rgba(41, 150, 161, 1);
  --color-red-400: rgba(255, 84, 89, 1);
  --color-red-500: rgba(192, 21, 47, 1);
  --color-orange-400: rgba(230, 129, 97, 1);
  --color-orange-500: rgba(168, 75, 47, 1);

  /* RGB versions for opacity control */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  --color-slate-500-rgb: 98, 108, 113;
  --color-red-500-rgb: 192, 21, 47;
  --color-red-400-rgb: 255, 84, 89;
  --color-orange-500-rgb: 168, 75, 47;
  --color-orange-400-rgb: 230, 129, 97;

  /* Background color tokens (Light Mode) */
  --color-bg-1: rgba(59, 130, 246, 0.08); /* Light blue */
  --color-bg-2: rgba(245, 158, 11, 0.08); /* Light yellow */
  --color-bg-3: rgba(34, 197, 94, 0.08); /* Light green */
  --color-bg-4: rgba(239, 68, 68, 0.08); /* Light red */
  --color-bg-5: rgba(147, 51, 234, 0.08); /* Light purple */
  --color-bg-6: rgba(249, 115, 22, 0.08); /* Light orange */
  --color-bg-7: rgba(236, 72, 153, 0.08); /* Light pink */
  --color-bg-8: rgba(6, 182, 212, 0.08); /* Light cyan */

  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);
  --color-select-caret: rgba(var(--color-slate-900-rgb), 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    /* RGB versions for opacity control (Dark Mode) */
    --color-gray-400-rgb: 119, 124, 124;
    --color-teal-300-rgb: 50, 184, 198;
    --color-gray-300-rgb: 167, 169, 169;
    --color-gray-200-rgb: 245, 245, 245;

    /* Background color tokens (Dark Mode) */
    --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
    --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
    --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
    --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
    --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
    --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
    --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
    --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
    
    /* Semantic Color Tokens (Dark Mode) */
    --color-background: var(--color-charcoal-700);
    --color-surface: var(--color-charcoal-800);
    --color-text: var(--color-gray-200);
    --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
    --color-primary: var(--color-teal-300);
    --color-primary-hover: var(--color-teal-400);
    --color-primary-active: var(--color-teal-800);
    --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
    --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
    --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
    --color-border: rgba(var(--color-gray-400-rgb), 0.3);
    --color-error: var(--color-red-400);
    --color-success: var(--color-teal-300);
    --color-warning: var(--color-orange-400);
    --color-info: var(--color-gray-300);
    --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
    --color-btn-primary-text: var(--color-slate-900);
    --color-card-border: rgba(var(--color-gray-400-rgb), 0.2);
    --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: var(--color-teal-300-rgb);
    --color-error-rgb: var(--color-red-400-rgb);
    --color-warning-rgb: var(--color-orange-400-rgb);
    --color-info-rgb: var(--color-gray-300-rgb);
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  /* RGB versions for opacity control (dark mode) */
  --color-gray-400-rgb: 119, 124, 124;
  --color-teal-300-rgb: 50, 184, 198;
  --color-gray-300-rgb: 167, 169, 169;
  --color-gray-200-rgb: 245, 245, 245;

  /* Colorful background palette - Dark Mode */
  --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
  --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
  --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
  --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
  --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
  --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
  --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
  --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
  
  /* Semantic Color Tokens (Dark Mode) */
  --color-background: var(--color-charcoal-700);
  --color-surface: var(--color-charcoal-800);
  --color-text: var(--color-gray-200);
  --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
  --color-primary: var(--color-teal-300);
  --color-primary-hover: var(--color-teal-400);
  --color-primary-active: var(--color-teal-800);
  --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
  --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
  --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
  --color-border: rgba(var(--color-gray-400-rgb), 0.3);
  --color-error: var(--color-red-400);
  --color-success: var(--color-teal-300);
  --color-warning: var(--color-orange-400);
  --color-info: var(--color-gray-300);
  --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
  --color-btn-primary-text: var(--color-slate-900);
  --color-card-border: rgba(var(--color-gray-400-rgb), 0.15);
  --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
  --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: var(--color-teal-300-rgb);
  --color-error-rgb: var(--color-red-400-rgb);
  --color-warning-rgb: var(--color-orange-400-rgb);
  --color-info-rgb: var(--color-gray-300-rgb);
}

[data-color-scheme="light"] {
  /* RGB versions for opacity control (light mode) */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  
  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: var(--color-teal-500-rgb);
  --color-error-rgb: var(--color-red-500-rgb);
  --color-warning-rgb: var(--color-orange-500-rgb);
  --color-info-rgb: var(--color-slate-500-rgb);
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* END PERPLEXITY DESIGN SYSTEM */
/* Custom styles for Smart City Management Platform */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', var(--font-family-base);
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    color: var(--color-text);
    overflow-x: hidden;
}

.app {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Navigation */
.sidebar {
    width: 260px;
    background: rgba(15, 15, 25, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(0, 206, 209, 0.2);
    position: fixed;
    height: 100vh;
    z-index: 100;
    overflow-y: auto;
}

.sidebar-header {
    padding: var(--space-24);
    border-bottom: 1px solid rgba(0, 206, 209, 0.2);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--space-12);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: #00CED1;
}

.logo-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(45deg, #00CED1, #32CD32);
    border-radius: var(--radius-base);
    position: relative;
}

.logo-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    background: #0a0a0a;
    border-radius: var(--radius-sm);
}

.nav-menu {
    list-style: none;
    padding: var(--space-16);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--space-12);
    padding: var(--space-12) var(--space-16);
    margin-bottom: var(--space-8);
    border-radius: var(--radius-base);
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-standard);
    position: relative;
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, #00CED1, #32CD32);
    transition: width var(--duration-normal) var(--ease-standard);
    opacity: 0.1;
}

.nav-item:hover::before,
.nav-item.active::before {
    width: 100%;
}

.nav-item:hover,
.nav-item.active {
    color: #00CED1;
    background: rgba(0, 206, 209, 0.1);
    border-left: 3px solid #00CED1;
}

.nav-icon {
    font-size: var(--font-size-lg);
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Main Content */
.main-content {
    margin-left: 260px;
    flex: 1;
    background: linear-gradient(135deg, #0f0f19 0%, #1a1a2e 100%);
    min-height: 100vh;
    position: relative;
}

.main-content::before {
    content: '';
    position: fixed;
    top: 0;
    left: 260px;
    right: 0;
    height: 100vh;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a"><stop offset="0" stop-color="%2300CED1" stop-opacity="0.1"/><stop offset="1" stop-color="%2300CED1" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="400" r="150" fill="url(%23a)"/><circle cx="400" cy="800" r="80" fill="url(%23a)"/></svg>');
    pointer-events: none;
    z-index: 1;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-24) var(--space-32);
    background: rgba(15, 15, 25, 0.8);
    backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(0, 206, 209, 0.2);
    position: relative;
    z-index: 10;
}

.header-left h1 {
    font-size: var(--font-size-3xl);
    background: linear-gradient(45deg, #00CED1, #32CD32);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.current-time {
    color: var(--color-text-secondary);
    font-size: var(--font-size-base);
    margin-top: var(--space-4);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--space-24);
}

.weather-widget {
    background: rgba(0, 206, 209, 0.1);
    padding: var(--space-16);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(0, 206, 209, 0.3);
}

.weather-info {
    display: flex;
    align-items: center;
    gap: var(--space-12);
    margin-bottom: var(--space-8);
}

.temperature {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: #00CED1;
}

.weather-details {
    display: flex;
    gap: var(--space-16);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: var(--space-12);
}

.avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #00CED1, #32CD32);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}

/* Section Management */
.section {
    padding: var(--space-32);
    display: none;
    position: relative;
    z-index: 5;
}

.section.active {
    display: block;
}

.section h2 {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--space-32);
    background: linear-gradient(45deg, #00CED1, #32CD32);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-24);
    margin-bottom: var(--space-32);
}

.metric-card {
    background: rgba(15, 15, 25, 0.8);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 206, 209, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
    position: relative;
    overflow: hidden;
    transition: all var(--duration-normal) var(--ease-standard);
    cursor: pointer;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #00CED1, #32CD32);
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-standard);
}

.metric-card:hover::before {
    opacity: 1;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 206, 209, 0.2);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-16);
}

.metric-header h3 {
    font-size: var(--font-size-lg);
    color: var(--color-text);
}

.metric-icon {
    font-size: var(--font-size-2xl);
    opacity: 0.8;
}

.metric-value {
    display: flex;
    align-items: baseline;
    gap: var(--space-8);
    margin-bottom: var(--space-16);
}

.metric-value .value {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: #00CED1;
    text-shadow: 0 0 20px rgba(0, 206, 209, 0.3);
}

.metric-value .unit {
    font-size: var(--font-size-lg);
    color: var(--color-text-secondary);
}

/* Progress Indicators */
.metric-progress {
    height: 6px;
    background: rgba(0, 206, 209, 0.2);
    border-radius: var(--radius-full);
    margin-bottom: var(--space-16);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #00CED1, #32CD32);
    border-radius: var(--radius-full);
    transition: width 1s ease-in-out;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Circular Progress */
.circular-progress {
    width: 80px;
    height: 80px;
    margin: var(--space-16) 0;
    position: relative;
}

.circular-progress svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.circular-progress circle:last-child {
    stroke-dasharray: 0 100;
    animation: circleProgress 2s ease-in-out forwards;
}

@keyframes circleProgress {
    to {
        stroke-dasharray: 76 100;
    }
}

/* Air Quality Specific */
.air-quality-status {
    display: inline-block;
    padding: var(--space-6) var(--space-12);
    background: rgba(255, 215, 0, 0.2);
    color: #FFD700;
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--space-16);
}

.air-quality-details {
    display: flex;
    justify-content: space-between;
}

.air-quality-details .detail {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    font-size: var(--font-size-sm);
}

/* Water Indicator */
.water-indicator {
    width: 60px;
    height: 100px;
    background: rgba(0, 206, 209, 0.2);
    border-radius: 0 0 30px 30px;
    margin: var(--space-16) auto;
    position: relative;
    overflow: hidden;
}

.water-level {
    position: absolute;
    bottom: 0;
    width: 100%;
    background: linear-gradient(0deg, #00CED1, #32CD32);
    border-radius: 0 0 30px 30px;
    animation: waterFill 2s ease-in-out;
}

@keyframes waterFill {
    from { height: 0; }
    to { height: 65%; }
}

/* Waste Bins */
.waste-bins {
    display: flex;
    justify-content: center;
    gap: var(--space-12);
    margin: var(--space-16) 0;
}

.bin {
    width: 20px;
    height: 40px;
    background: rgba(255, 105, 180, 0.2);
    border-radius: var(--radius-sm);
    position: relative;
    overflow: hidden;
}

.bin-fill {
    position: absolute;
    bottom: 0;
    width: 100%;
    background: linear-gradient(0deg, #FF69B4, #FF1493);
    transition: height 1s ease-in-out;
}

/* Population Animation */
.population-animation {
    position: relative;
    width: 60px;
    height: 60px;
    margin: var(--space-16) auto;
}

.pulse-ring {
    position: absolute;
    border: 2px solid #00CED1;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    opacity: 0;
    animation: pulse 3s infinite;
}

.pulse-ring:nth-child(2) { animation-delay: 1s; }
.pulse-ring:nth-child(3) { animation-delay: 2s; }

@keyframes pulse {
    0% {
        transform: scale(0.1);
        opacity: 1;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

/* Trends */
.metric-trend {
    display: flex;
    align-items: center;
    gap: var(--space-8);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

.trend {
    font-weight: var(--font-weight-medium);
}

.trend.positive {
    color: #32CD32;
}

.trend.negative {
    color: #FF6B35;
}

/* Dashboard Row */
.dashboard-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-32);
    margin-bottom: var(--space-32);
}

.chart-container,
.map-container {
    background: rgba(15, 15, 25, 0.8);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 206, 209, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
}

.chart-container h3,
.map-container h3 {
    margin-bottom: var(--space-16);
    color: #00CED1;
}

/* City Map */
.city-map {
    height: 300px;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border-radius: var(--radius-base);
    position: relative;
    overflow: hidden;
}

.city-map::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(0,206,209,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

.sensor-marker {
    position: absolute;
    width: 12px;
    height: 12px;
    background: #00CED1;
    border-radius: 50%;
    cursor: pointer;
    transform: translate(-50%, -50%);
}

.sensor-marker.warning {
    background: #FFD700;
    animation: warningPulse 2s infinite;
}

@keyframes warningPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.marker-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border: 2px solid currentColor;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: markerPulse 2s infinite;
}

@keyframes markerPulse {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

.marker-label {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(15, 15, 25, 0.9);
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    transition: opacity var(--duration-fast) var(--ease-standard);
    pointer-events: none;
}

.sensor-marker:hover .marker-label {
    opacity: 1;
}

/* Alerts Panel */
.alerts-panel {
    background: rgba(15, 15, 25, 0.8);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 206, 209, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
}

.alerts-panel h3 {
    margin-bottom: var(--space-16);
    color: #00CED1;
}

.alert-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-12);
}

.alert-item {
    display: flex;
    align-items: flex-start;
    gap: var(--space-12);
    padding: var(--space-16);
    border-radius: var(--radius-base);
    transition: all var(--duration-fast) var(--ease-standard);
}

.alert-item.warning {
    background: rgba(255, 215, 0, 0.1);
    border-left: 3px solid #FFD700;
}

.alert-item.info {
    background: rgba(0, 206, 209, 0.1);
    border-left: 3px solid #00CED1;
}

.alert-item.error {
    background: rgba(255, 107, 53, 0.1);
    border-left: 3px solid #FF6B35;
}

.alert-icon {
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.alert-content p {
    margin: 0 0 var(--space-4) 0;
    font-weight: var(--font-weight-medium);
}

.alert-time {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

/* Analytics Section */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--space-32);
}

.analytics-card {
    background: rgba(15, 15, 25, 0.8);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 206, 209, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
}

.analytics-card h3 {
    margin-bottom: var(--space-16);
    color: #00CED1;
}

/* Sensors Grid */
.sensors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-24);
}

.sensor-card {
    background: rgba(15, 15, 25, 0.8);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 206, 209, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--space-20);
    transition: all var(--duration-normal) var(--ease-standard);
}

.sensor-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0, 206, 209, 0.2);
}

.sensor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-12);
}

.sensor-id {
    font-weight: var(--font-weight-bold);
    color: #00CED1;
}

.sensor-status {
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

.sensor-status.online {
    background: rgba(50, 205, 50, 0.2);
    color: #32CD32;
}

.sensor-status.warning {
    background: rgba(255, 215, 0, 0.2);
    color: #FFD700;
}

.sensor-status.offline {
    background: rgba(255, 107, 53, 0.2);
    color: #FF6B35;
}

.sensor-type {
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-8);
}

.sensor-value {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: #00CED1;
    margin-bottom: var(--space-8);
}

.sensor-location {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

/* Alerts Container */
.alerts-container {
    background: rgba(15, 15, 25, 0.8);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 206, 209, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
}

.alerts-filter {
    display: flex;
    gap: var(--space-12);
    margin-bottom: var(--space-24);
}

.filter-btn {
    background: rgba(0, 206, 209, 0.1);
    border: 1px solid rgba(0, 206, 209, 0.3);
    color: var(--color-text);
    padding: var(--space-8) var(--space-16);
    border-radius: var(--radius-base);
    cursor: pointer;
    transition: all var(--duration-fast) var(--ease-standard);
}

.filter-btn:hover,
.filter-btn.active {
    background: rgba(0, 206, 209, 0.2);
    border-color: #00CED1;
    color: #00CED1;
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-24);
}

.service-control {
    background: rgba(15, 15, 25, 0.8);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 206, 209, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
}

.service-control h3 {
    margin-bottom: var(--space-16);
    color: #00CED1;
}

.control-panel {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-12);
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(119, 124, 124, 0.3);
    transition: var(--duration-normal);
    border-radius: 24px;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: var(--duration-normal);
    border-radius: 50%;
}

.toggle-switch input:checked + label {
    background-color: #00CED1;
}

.toggle-switch input:checked + label:before {
    transform: translateX(26px);
}

.control-status {
    font-weight: var(--font-weight-medium);
    color: #32CD32;
}

.service-stats {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

/* Settings Content */
.settings-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-32);
}

.settings-group {
    background: rgba(15, 15, 25, 0.8);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 206, 209, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
}

.settings-group h3 {
    margin-bottom: var(--space-16);
    color: #00CED1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        transform: translateX(-100%);
        transition: transform var(--duration-normal) var(--ease-standard);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .main-content::before {
        left: 0;
    }
    
    .dashboard-row {
        grid-template-columns: 1fr;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .header {
        padding: var(--space-16);
    }
    
    .section {
        padding: var(--space-16);
    }
}

/* Loading Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn var(--duration-normal) var(--ease-standard);
}

/* Chart wrapper for proper sizing */
.chart-wrapper {
    background: rgba(0, 206, 209, 0.05);
    border-radius: var(--radius-base);
    padding: var(--space-16);
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 206, 209, 0.3);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 206, 209, 0.5);
}